## 改进措施
1. 增加网络隐藏层数量
2. 增加地理特征调制

## 实验结果
模型收敛速度变慢，由7秒一个batch变为10秒
恢复水体形态更好
训练初期出现许多随机噪声，但很快随着训练深入而减少

训练到第6轮，IOU达到0.85，F1达到0.92


# 第二次试验

## 改进措施
1. 增加网络深度，减少宽度
2. 只在前三层增加地理特征调制

训练初期同样出现许多随机噪声

训练到第9轮
Best F1 Score:  0.9201
Best IoU:       0.8621

基准精度
IOU达到0.85，F1达到0.92

# 第三次实验
## 改进措施
1. 使用3D卷积、1D+2D卷积、编码器-解码器等方式压缩时间信息，然后使用2D卷积

## 实验结果
训练速度变慢，内存占用更大，精度变差
训练第三轮

Best Val Loss:  0.2210
Best F1 Score:  0.8385
Best IoU:       0.7672

# 第四次试验
## 改进措施
1. 输入36幅图像，实现完全的3D卷积
2. 删除门控注意力部分，简化注意力的运算

## 实验结果
训练速度变慢，内存占用更大
训练第五轮
Best Val Loss:  0.2719
Best F1 Score:  0.6635
Best IoU:       0.5905

## 第五次实验
在第二次实验基础上，将特征融合后的3D卷积核换为2D

## 实验结果
在精度、训练速度与第二次实验区别不大

## 第六次实验
在第五次实验基础上，将Loss函数换为更复杂的: 边界损失、Focal Loss、DICE Loss

损失虽然在下降，但是评价指标和可视化效果不好

## 第七次实验
将Loss函数换为：BCE、Focal、DICE

可视化结果仍然是一团浆糊，且损失下降很慢

## 第八次试验
缩小网络宽度和深度，减少参数量

训练速度很快，但是上限较低
第八轮
Best Val Loss:  0.1658
Best F1 Score:  0.9181
Best IoU:       0.8572

# 第九次试验
将初始层改为多尺度卷积

第十三轮
Best Val Loss:  0.1437
Best F1 Score:  0.9190
Best IoU:       0.8598

## 结论
效果提升并不明显

# 第十次实验
## 改进措施
添加SE通道注意力
引入循环学习率
在A100运行

## 实验结果
循环学习率的训练效果更好
对模型的修改影响效果
在A100的小批量影响训练效果，批量大小为8左右比较好

修正了上采样的链接
第三轮
Best Val Loss:    0.2747
Best F1 Score:    0.8131
Best IoU:         0.7285

移除SE通道注意力，移除多尺度卷积
Best Val Loss:    0.2894
Best F1 Score:    0.8394
Best IoU:         0.7622

# 第11次试验
## 改进措施
在HPC上运行
批量设置为8

# 试验结果
在输入引入多尺度卷积效果不行
Total Epochs:     3
Best Val Loss:    0.2513
Best F1 Score:    0.8710
Best IoU:         0.7975

## 结论
删除多尺度卷积

# 第12次试验
## 测试
精简模型
将输入特征提取改为大卷积核，减小特征图
精度变差，因为损失了信息

# 第13次试验
开展temporal、seasonal、spatial attention、attention gate、geo的消融实验

实验结论
发现删除attention gate比基准更好
发现60M模型远远比30M模型好
3D Unet效果低于2D Unet

60M 2D
Total Epochs:     10
Best Val Loss:    0.0933
Best F1 Score (0-1):    0.9724
Best IoU (0-1):         0.9505
Best F1 Score (0.2-0.8):0.7292
Best IoU (0.2-0.8):     0.5945
Best F1 Score (0.4-0.6):0.6828
Best IoU (0.4-0.6):     0.5390

30M 2D
Best F1 Score (0-1):    0.9143
Best IoU (0-1):         0.8533
Best F1 Score (0.2-0.8):0.6556
Best IoU (0.2-0.8):     0.5089
Best F1 Score (0.4-0.6):0.5851
Best IoU (0.4-0.6):     0.4341

3D Unet
Epoch 4/10 Summary (Time: 891.3s):
Train Loss:     0.1433
Val Loss:       0.1237
Accuracy (0-1):  0.9630
Precision (0-1): 0.7669
Recall (0-1):    0.9545
F1 Score (0-1):  0.8055
IoU (0-1):       0.7526
Accuracy (0.2-0.8):0.6908
Precision (0.2-0.8):0.5887
Recall (0.2-0.8): 0.8382
F1 Score (0.2-0.8):0.6601
IoU (0.2-0.8):     0.5314
Accuracy (0.4-0.6):0.6295
Precision (0.4-0.6):0.5510
Recall (0.4-0.6): 0.8354
F1 Score (0.4-0.6):0.6234
IoU (0.4-0.6):     0.5005

# 第14次试验
IO读取会严重拖慢速度
因此，一次读取大文件（100M），再一次导出大文件（100）可以大幅提升效率
基于此，修改了clip.py函数，将data组织为（idx_x, idx_y, time, y, x）五维格式


clip.py运行速度太慢，解决方法
设置gdal的参数
`python
os.environ.setdefault("GDAL_DISABLE_READDIR_ON_OPEN", "YES")  # 避免 ReadDir 阻塞
os.environ.setdefault("GDAL_NUM_THREADS", str(CPU_COUNT))       # GDAL 内部线程数
os.environ.setdefault("GDAL_CACHEMAX", "512")                  # GDAL 缓存，单位 MB

_rio_env = rasterio.Env()
_rio_env.__enter__()
`

启用joblib的线程模式
tile_results = Parallel(n_jobs=processes, prefer="threads", batch_size="auto")(
    delayed(process_tile_from_cache)(args) for args in tasks
)

Experiment results
the clip.py is still running slowly, so we need to find a more effieient way.

# The 15th Experiment
apply video swin transformer to restore the missing area of surface water
design the water frequency-guided loss function
introduce the attention and distance mechanism to select key frames in 120 time series

## Experiment results
the training cannot focuse on the midian water freuqncy to optimize parameters
epoch 3
Acc(0-1) = 0.9247, F1(0-1)=0.9246, IoU(0-1)=0.8598, 
Acc(0.2-0.8) = 0.9190, F1(0.2-0.8)=0.3226, IoU(0.2-0.8)=0.1923, 
Acc(0.4-0.6) = 0.6000, F1(0.4-0.6)=0.5909, IoU(0.4-0.6)=0.4194

epoch 5
Acc(0-1) = 0.9566, F1(0-1)=0.9513, IoU(0-1)=0.9071, 
Acc(0.2-0.8) = 0.8530, F1(0.2-0.8)=0.1397, IoU(0.2-0.8)=0.0751, 
Acc(0.4-0.6) = 0.4194, F1(0.4-0.6)=0.2623, IoU(0.4-0.6)=0.1509

Epoch 17
Acc(0-1) = 0.9829, F1(0-1)=0.9812, IoU(0-1)=0.9631, 
Acc(0.2-0.8) = 0.8619, F1(0.2-0.8)=0.1333, IoU(0.2-0.8)=0.0714, 
Acc(0.4-0.6) = 0.5408, F1(0.4-0.6)=0.2623, IoU(0.4-0.6)=0.1509

## Reason
we don't apply the "use_frequency_weight" as true in configs.

# The 16th Experiment
1. 架构层面的优化
AttentionGuidedPyramidSampling增强
✅ 增加了水体频率信息作为输入
✅ 计算频率不确定性分数：u = 1 - |freq - 0.5| * 2
✅ 动态调整采样策略，对高不确定性区域使用更宽的时间窗口
TemporalAttentionModule增强
✅ 添加了轻量级GRU用于时序建模
✅ 仅对频率在[0.2, 0.8]范围内的动态patch应用GRU
✅ 其他静态区域跳过GRU以节省计算
MemoryEfficientDecoder增强
✅ 添加了skip connection gates进行动态加权
✅ 根据水体频率信息调整skip权重
✅ 对动态区域(freq≈0.5)给予更多关注
2. 损失函数优化
频率权重曲线重新设计
✅ 从原来的"∪形"改为"鞍形"：w = 1 + α * exp(-((f-0.5)/σ)²)
✅ α=2.0, σ=0.15，使频率0.5附近的权重达到3倍
✅ 大幅提升对动态水体区域的损失权重
Focal Loss支持
✅ 添加了可选的Focal Loss，γ=2
✅ 对困难样本(预测概率偏低的)给予更大权重
✅ 有助于模型关注难以分类的动态边界
3. 关键改进点
多尺度频率感知：从采样到解码的全流程都考虑了水体频率信息
动态区域专门优化：GRU仅在需要的地方运行，既提升效果又控制计算量
梯度增强：通过新的损失权重曲线，动态区域获得3倍梯度信号
自适应特征融合：skip connection根据动态程度自动调整权重

## Experiment results
2025-06-27 18:57:54,073 - INFO - Epoch 12: Val Loss=0.1359, 
Acc (0-1) = 0.9974, F1 (0-1)=0.9970, IoU (0-1)=0.9940, 
Acc (0.2-0.8) = 0.7717, F1 (0.2-0.8)=0.4549, IoU (0.2-0.8)=0.3732, 
Acc (0.4-0.6) = 0.8277, F1 (0.4-0.6)=0.4710, IoU (0.4-0.6)=0.4075

# The 17th Experiment
train model in a large number of samples

## Experiment results
2025-06-29 10:39:17,629 - INFO - New best model (0.4-0.6 freq)! Acc=0.7909, F1=0.7780, IoU=0.6712
2025-06-29 10:39:17,629 - INFO - Epoch 10: Val Loss=0.1488, 
Acc (0-1) = 0.9791, F1 (0-1)=0.9752, IoU (0-1)=0.9527, 
Acc (0.2-0.8) = 0.8095, F1 (0.2-0.8)=0.8028, IoU (0.2-0.8)=0.6960, 
Acc (0.4-0.6) = 0.7909, F1 (0.4-0.6)=0.7780, IoU (0.4-0.6)=0.6712

日志记录: train_multiGPUs.out
# The 18th Experiment
simplify the model

## Experiment results
模型预测结果都是背景值
iou长期为0

可能的问题：
1. 关键帧 → 聚合 → 只解码中心帧：信息被两次压缩
• AttentionGuidedPyramidSampling / SoftTopKFrameAggregator 先把 120 帧压到 24 帧；
然后 TemporalAttentionModule 把 24 帧输出再展平成序列；
进入 Encoder 后，又在解码前只取 “中心帧” 特征 (selected_features 把所有 T 维度再砍掉)。
→ 训练阶段，loss 仅根据中心帧像素计算；其余时间信息仅通过注意力残差参与梯度。
如果聚合器初始参数没学好，中心帧通道在 Early stage 基本全是背景 → tp=0。
建议：
a) 先别砍掉 T，在 decoder 里使用 3D 上采样或 Conv + reshape，保留一些时序信息；
b) 或者直接用 center_frame_idx 把原序列切 16 帧窗口喂给 Swin，再 decode。
2. PatchEmbedding 固定 32×32 grid + 缺少 2D 位置编码
• EfficientPatchEmbed 只做一次 Conv 并用 LayerNorm，没有任何 (H,W) 位置编码；
Swin 自身的 WindowAttention 使用相对位置偏置，但在 cross-frame flatten 之前 H,W 维度就被打乱过多次( window_partition → window_reverse → TemporalSeparableAttention )。
→ 网络要靠权重自己学会 “哪个 token 在哪里”；在小数据/稀疏目标任务里很难。
建议：
a) 在 EfficientPatchEmbed 后加简单的 learnable_pos_embed (N٬D)；
b) 或考虑把传统 Swin 的 patch_embed + sin/cos PE 拿回来。
3. SoftTopKFrameAggregator 的 “Top-K+复制” 机制
聚合器最后把加权和 aggregated_single 简单复制成 24 帧，再乘一个 sin(time) 调制。
这给 Encoder 造成假象：所有帧几乎一样，只差 0.1×sin，相当于 极端低频 时序信号。
TemporalAttention 模块学不到区分帧的动机，输出又回到“全背景”。
→ 可以改为直接输出 1 帧 (真正聚合好的) 让后端走纯 2D 分割路径；
或者保留 Top-K 原帧而非复制的 fake 帧。

# The 19th Experiment

在17th实验基础上，加入了可视化和精度历史记录部分

对比是否启用focal loss
启用
train_multiGPUs
不启用
train.out

在17th实验基础上，简化模型
train_refactroed.out
实验结果：
训练不稳定

# The 20th Experiment
v4: patch尺度的选择。内存占用过大
v5: 使用可学习的帧选择方法。训练不稳定，速度不如v1。
v6: 使用卷积网络提取特征再导入transformer

原始版本不如v1
v1不如v5

v5 使用相对动态权重无法有效提升0.4-0.6频率区间精度
v5_2 保持使用动态权重曲线
v5_3 使用线性的动态权重曲线
v5_4 删除注意力偏置
v5_5 删除动态权重在模型的注意力偏置、门控，不使用GRU层。使用不重复尺度抽样
v5_5_focal 使用focal loss，不使用动态权重
v5_6_glad 将动态权重最大值增加到2
v6 多尺度帧选择、多尺度注意力聚合、删除GRU层，删除所有动态权重引导
v6_2 优化模型架构

v8 3Dswin
v9 3Dswin+TargetGuided

加上TargetGuided，模型数值变得不稳定，且收敛速度变慢
加上之后，第一轮val loss为0.3，加上之前，第一轮val loss为0.2
WARNING:model.swin_water_net_v9:NaN/Inf detected in target_query, applying correction

全数值精度下重新测试
v8 3Dswin
v9 3Dswin+TargetGuided
v9_test 时间窗口增加为6
v10 以目标帧索引其他时间步

v9实验效果很好 Acc (0.4-0.6) = 0.8454, F1 (0.4-0.6)=0.6899, IoU (0.4-0.6)=0.6129
v9_test实验效果没有显著提高 Acc (0.4-0.6) = 0.8390, F1 (0.4-0.6)=0.6198, IoU (0.4-0.6)=0.5507

v9的收敛速度更快
v10的内存消耗减少接近一半

Focal Loss能够让收敛速度更快，精度指标更高


# The 21th Experiment
v9 3Dswim+TargetGuided+Focal loss and DICE loss (frequency weight)
v8 3Dswim



v10 目标帧为索引 + Focal loss and DICE loss
v5 原始模型 去除关键帧选取 Focal loss and DICE loss

v5原始模型结果在0.4-0.6表现很差
INFO:__main__:Validation v5.1 | Epoch 2: Loss=0.0799
INFO:__main__:Metrics (0-1): Acc=0.9911, F1=0.9290, IoU=0.9157
INFO:__main__:Metrics (0.4-0.6): Acc=0.5371, F1=0.2945, IoU=0.2164

损失函数值很低，但是在0.4-0.6指标表现不好，说明Focal loss and DICE loss损失函数没有针对高动态区域强化

# The 22th Experiment
使用了BCE+DIC loss，去除水体频率加权，缺失率限制在0.05-0.90
v10模型取得了最好效果
INFO:__main__:New best model (0.4-0.6 freq)! Acc=0.8796, F1=0.8137, IoU=0.7173
INFO:__main__:Epoch 1: Val Loss=0.1036,
Acc (0-1) = 0.9498, F1 (0-1)=0.9431, IoU (0-1)=0.8949,
Acc (0.2-0.8) = 0.8901, F1 (0.2-0.8)=0.8584, IoU (0.2-0.8)=0.7681,
Acc (0.4-0.6) = 0.8796, F1 (0.4-0.6)=0.8137, IoU (0.4-0.6)=0.7173

v13 使用动态patch_size
v14 使用水体频率对不同patch_size进行加权融合
v15 使用样本间不同的patch_size
v16 使用固定的patch_size

v13的收敛速度较慢
v14模型收敛速度比v10和v13的收敛速度都更快
v15训练初期较难找到合适的水体频率与patch_size关系，水体频率0-1的精度较低
v16使用patch_size 8的时候，精度不如v14
v16使用patch_size 4的时候，精度与v14相近

使用频率加权损失函数对于提升0.4-0.6频率区域的精度没有帮助，反而会降低F1分数
v16 使用频率加权损失函数
INFO:__main__:Epoch 2: Val Loss=0.1933, 
Acc (0-1) = 0.9405, F1 (0-1)=0.9154, IoU (0-1)=0.8620, 
Acc (0.2-0.8) = 0.8365, F1 (0.2-0.8)=0.7428, IoU (0.2-0.8)=0.6573, 
Acc (0.4-0.6) = 0.8139, F1 (0.4-0.6)=0.6933, IoU (0.4-0.6)=0.6169

将缺失率1单独作为一类采样，让精度指标表现有所下降，所以后续使用0.05, 0.2, 0.4, 0.6, 0.8, 1.0作为五类采样，适当降低缺失率为1的采样数量，有助于精度指标提升
v16 将缺失率1单独作为一类采样
INFO:__main__:Epoch 0: Val Loss=0.1690, 
Acc (0-1) = 0.9224, F1 (0-1)=0.9177, IoU (0-1)=0.8520, 
Acc (0.2-0.8) = 0.7954, F1 (0.2-0.8)=0.7369, IoU (0.2-0.8)=0.6225, 
Acc (0.4-0.6) = 0.7794, F1 (0.4-0.6)=0.6846, IoU (0.4-0.6)=0.5652

通过在前两个stage使用时空注意力，相当于增加感受野，第一个stage的感受野是patch_size 4，增加到第二个stage，感受野变为8。同时，增加前两个stage的层数到4, 4，增强时间建模。
2025-07-28 21:16:47 INFO Epoch 0: Val Loss=0.1672, 
Acc (0-1) = 0.9372, F1 (0-1)=0.9341, IoU (0-1)=0.8786, 
Acc (0.2-0.8) = 0.8179, F1 (0.2-0.8)=0.8131, IoU (0.2-0.8)=0.7090, 
Acc (0.4-0.6) = 0.8046, F1 (0.4-0.6)=0.7790, IoU (0.4-0.6)=0.6841


如果4个stage都使用时空注意力，精度反而下降
2025-07-29 10:48:09 INFO Epoch 0: Val Loss=0.1148, 
Acc (0-1) = 0.9489, F1 (0-1)=0.9137, IoU (0-1)=0.8661, 
Acc (0.2-0.8) = 0.7873, F1 (0.2-0.8)=0.6898, IoU (0.2-0.8)=0.5893, 
Acc (0.4-0.6) = 0.7505, F1 (0.4-0.6)=0.6248, IoU (0.4-0.6)=0.5274

训练完成

以下任务：
选择精度指标最好的模型
创建验证点，与现在使用的样本都不同
在验证点上评价模型精度
绘制验证点的全球分布图，以及水体频率、水体比例、缺失比例、年、月直方图
确认模型有效后，在青藏高原区域使用模型生产数据

# The 23th Experiment
v20 前两个stage使用时间注意力
v21 交叉应用时间注意力和空间注意力

v21的表现并没有比v20更好，反而在F1分数更差